<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Microphone Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .record { background: #4CAF50; color: white; }
        .stop { background: #f44336; color: white; }
        .test { background: #2196F3; color: white; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        #log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            white-space: pre-wrap;
        }
        #status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }
        .recording { background: #ffebee; color: #c62828; }
        .processing { background: #fff3e0; color: #ef6c00; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .error { background: #ffebee; color: #c62828; }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Simple Microphone Test</h1>
        
        <div>
            <label for="apiKey">API Key:</label>
            <input type="text" id="apiKey" placeholder="Enter API key" value="resEY0jeJ5KqTFyV3xaLineuRMgBqvsSneEiUG_Ic4yJVVrPFSPGMlNM3ySVzznE">
        </div>
        
        <div>
            <button id="startBtn" class="record">🎤 Start Recording</button>
            <button id="stopBtn" class="stop" disabled>⏹️ Stop Recording</button>
            <button id="testBtn" class="test">🧪 Test Conversion</button>
        </div>
        
        <div id="status" style="display: none;"></div>
        
        <div id="log">Ready to test microphone...\n</div>
    </div>

    <script>
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;

        // Get elements
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const testBtn = document.getElementById('testBtn');
        const status = document.getElementById('status');
        const logElement = document.getElementById('log');
        const apiKeyInput = document.getElementById('apiKey');

        // Logging function
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // Status function
        function showStatus(message, type) {
            status.textContent = message;
            status.className = type;
            status.style.display = 'block';
        }

        // Your conversion functions
        async function convertMediaRecorderBlobToWav(blob, targetSampleRate = 16000) {
            log(`Starting conversion to ${targetSampleRate}Hz WAV...`);
            
            const arrayBuffer = await blob.arrayBuffer();
            log(`Audio buffer size: ${arrayBuffer.byteLength} bytes`);
            
            const audioCtx = new AudioContext();
            const decodedBuffer = await audioCtx.decodeAudioData(arrayBuffer);
            
            log(`Decoded: ${decodedBuffer.sampleRate}Hz, ${decodedBuffer.numberOfChannels}ch, ${decodedBuffer.duration.toFixed(2)}s`);
            
            // Resample
            const offlineCtx = new OfflineAudioContext(
                1,
                decodedBuffer.duration * targetSampleRate,
                targetSampleRate
            );
            
            const source = offlineCtx.createBufferSource();
            source.buffer = decodedBuffer;
            source.connect(offlineCtx.destination);
            source.start(0);
            
            log('Resampling audio...');
            const resampledBuffer = await offlineCtx.startRendering();
            
            log(`Resampled: ${resampledBuffer.sampleRate}Hz, ${resampledBuffer.numberOfChannels}ch`);
            
            // Encode to WAV
            log('Encoding to WAV...');
            return encodeWav(resampledBuffer, targetSampleRate);
        }

        function encodeWav(audioBuffer, sampleRate) {
            const channelData = audioBuffer.getChannelData(0); // mono
            const length = channelData.length * 2;
            const buffer = new ArrayBuffer(44 + length);
            const view = new DataView(buffer);

            const writeString = (offset, str) => {
                for (let i = 0; i < str.length; i++) {
                    view.setUint8(offset + i, str.charCodeAt(i));
                }
            };

            // RIFF header
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length, true);

            // PCM samples
            let offset = 44;
            for (let i = 0; i < channelData.length; i++) {
                const sample = Math.max(-1, Math.min(1, channelData[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }

            log(`WAV encoding complete: ${buffer.byteLength} bytes`);
            return new Blob([view], { type: 'audio/wav' });
        }

        // Event listeners
        startBtn.addEventListener('click', async () => {
            try {
                log('Requesting microphone access...');
                showStatus('Requesting microphone access...', 'processing');
                
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });
                
                log('Microphone access granted');
                
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];
                
                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                        log(`Audio chunk: ${event.data.size} bytes`);
                    }
                };
                
                mediaRecorder.onstop = async () => {
                    log('Processing recording...');
                    showStatus('Processing audio...', 'processing');
                    
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    log(`Original blob: ${audioBlob.size} bytes`);
                    
                    try {
                        const wavBlob = await convertMediaRecorderBlobToWav(audioBlob, 16000);
                        log(`Converted WAV: ${wavBlob.size} bytes`);
                        showStatus('Conversion successful!', 'success');
                    } catch (error) {
                        log(`Conversion error: ${error.message}`);
                        showStatus(`Conversion failed: ${error.message}`, 'error');
                    }
                    
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                };
                
                mediaRecorder.start(1000);
                isRecording = true;
                
                startBtn.disabled = true;
                stopBtn.disabled = false;
                showStatus('Recording...', 'recording');
                log('Recording started');
                
            } catch (error) {
                log(`Error: ${error.message}`);
                showStatus(`Error: ${error.message}`, 'error');
            }
        });

        stopBtn.addEventListener('click', () => {
            if (mediaRecorder && isRecording) {
                log('Stopping recording...');
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                isRecording = false;
            }
        });

        testBtn.addEventListener('click', () => {
            log('Testing conversion functions...');
            showStatus('Testing...', 'processing');
            
            // Create a simple test audio buffer
            const sampleRate = 44100;
            const duration = 1; // 1 second
            const audioCtx = new AudioContext();
            const buffer = audioCtx.createBuffer(1, sampleRate * duration, sampleRate);
            const channelData = buffer.getChannelData(0);
            
            // Generate a simple sine wave
            for (let i = 0; i < channelData.length; i++) {
                channelData[i] = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.5;
            }
            
            try {
                const wavBlob = encodeWav(buffer, sampleRate);
                log(`Test WAV created: ${wavBlob.size} bytes`);
                showStatus('Test successful!', 'success');
            } catch (error) {
                log(`Test error: ${error.message}`);
                showStatus(`Test failed: ${error.message}`, 'error');
            }
        });

        log('Simple test app initialized');
    </script>
</body>
</html>
