<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Microphone Recording Test - Dhruva Platform</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
        }
        
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: #f7fafc;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .record-btn {
            background: #48bb78;
            color: white;
        }
        
        .record-btn:hover {
            background: #38a169;
            transform: translateY(-2px);
        }
        
        .record-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }
        
        .stop-btn {
            background: #f56565;
            color: white;
        }
        
        .stop-btn:hover {
            background: #e53e3e;
            transform: translateY(-2px);
        }
        
        .test-btn {
            background: #4299e1;
            color: white;
        }
        
        .test-btn:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }
        
        .status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }
        
        .status.recording {
            background: #fed7d7;
            color: #c53030;
            border: 2px solid #feb2b2;
        }
        
        .status.processing {
            background: #fef5e7;
            color: #d69e2e;
            border: 2px solid #f6e05e;
        }
        
        .status.success {
            background: #c6f6d5;
            color: #2f855a;
            border: 2px solid #9ae6b4;
        }
        
        .status.error {
            background: #fed7d7;
            color: #c53030;
            border: 2px solid #feb2b2;
        }
        
        .log {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .config {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .config-item {
            display: flex;
            flex-direction: column;
        }
        
        label {
            font-weight: 600;
            margin-bottom: 5px;
            color: #4a5568;
        }
        
        input, select {
            padding: 8px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #4299e1;
        }
        
        .audio-info {
            background: #edf2f7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .audio-info h4 {
            margin: 0 0 10px 0;
            color: #2d3748;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        
        .info-item {
            background: white;
            padding: 8px 12px;
            border-radius: 6px;
            border-left: 4px solid #4299e1;
        }
        
        .info-label {
            font-size: 12px;
            color: #718096;
            font-weight: 600;
        }
        
        .info-value {
            font-size: 14px;
            color: #2d3748;
            font-weight: 600;
        }
        
        .hidden {
            display: none;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Microphone Recording Test</h1>
        <p style="text-align: center; color: #718096; margin-bottom: 30px;">
            Test microphone recording with 16kHz WAV conversion for ASR compatibility
        </p>
        
        <div class="section">
            <h3>Configuration</h3>
            <div class="config">
                <div class="config-item">
                    <label for="apiKey">API Key:</label>
                    <input type="text" id="apiKey" placeholder="Enter your API key" value="">
                </div>
                <div class="config-item">
                    <label for="language">Language:</label>
                    <select id="language">
                        <option value="en">English</option>
                        <option value="hi">Hindi</option>
                        <option value="ta">Tamil</option>
                        <option value="te">Telugu</option>
                        <option value="kn">Kannada</option>
                        <option value="ml">Malayalam</option>
                        <option value="bn">Bengali</option>
                        <option value="mr">Marathi</option>
                        <option value="gu">Gujarati</option>
                        <option value="pa">Punjabi</option>
                    </select>
                </div>
                <div class="config-item">
                    <label for="maxDuration">Max Duration (seconds):</label>
                    <input type="number" id="maxDuration" value="30" min="5" max="120">
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>Recording Controls</h3>
            <div class="controls">
                <button id="startBtn" class="record-btn">🎤 Start Recording</button>
                <button id="stopBtn" class="stop-btn" disabled>⏹️ Stop Recording</button>
                <button id="testBtn" class="test-btn">🧪 Test File Upload</button>
            </div>
            
            <div id="status" class="status hidden"></div>
            
            <div id="audioInfo" class="audio-info hidden">
                <h4>Audio Information</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Original Format</div>
                        <div class="info-value" id="originalFormat">-</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Original Size</div>
                        <div class="info-value" id="originalSize">-</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Converted Format</div>
                        <div class="info-value" id="convertedFormat">-</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Converted Size</div>
                        <div class="info-value" id="convertedSize">-</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Duration</div>
                        <div class="info-value" id="duration">-</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Sample Rate</div>
                        <div class="info-value" id="sampleRate">16kHz</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>ASR Results</h3>
            <div id="asrResult" style="padding: 15px; background: #f7fafc; border-radius: 8px; min-height: 50px; border: 2px dashed #cbd5e0;">
                <em style="color: #a0aec0;">ASR transcription will appear here...</em>
            </div>
        </div>
        
        <div class="section">
            <h3>Debug Log</h3>
            <div id="log" class="log">Ready to start recording...\n</div>
            <button onclick="clearLog()" style="margin-top: 10px; background: #718096; color: white;">Clear Log</button>
        </div>
        
        <input type="file" id="fileInput" accept="audio/*" style="display: none;">
    </div>

    <script src="app.js"></script>
</body>
</html>
