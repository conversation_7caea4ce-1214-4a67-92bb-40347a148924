// Microphone Recording Test App
class MicrophoneRecordingTest {
    constructor() {
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.isRecording = false;
        this.startTime = null;
        this.maxDuration = 30;
        
        this.initializeElements();
        this.setupEventListeners();
        this.log('Application initialized');
    }
    
    initializeElements() {
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.testBtn = document.getElementById('testBtn');
        this.status = document.getElementById('status');
        this.audioInfo = document.getElementById('audioInfo');
        this.asrResult = document.getElementById('asrResult');
        this.logElement = document.getElementById('log');
        this.fileInput = document.getElementById('fileInput');
        this.apiKeyInput = document.getElementById('apiKey');
        this.languageSelect = document.getElementById('language');
        this.maxDurationInput = document.getElementById('maxDuration');
    }
    
    setupEventListeners() {
        this.startBtn.addEventListener('click', () => this.startRecording());
        this.stopBtn.addEventListener('click', () => this.stopRecording());
        this.testBtn.addEventListener('click', () => this.testFileUpload());
        this.fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        this.maxDurationInput.addEventListener('change', (e) => {
            this.maxDuration = parseInt(e.target.value);
            this.log(`Max duration set to ${this.maxDuration} seconds`);
        });
    }
    
    log(message) {
        const timestamp = new Date().toLocaleTimeString();
        const logMessage = `[${timestamp}] ${message}\n`;
        this.logElement.textContent += logMessage;
        this.logElement.scrollTop = this.logElement.scrollHeight;
        console.log(message);
    }
    
    showStatus(message, type = 'info') {
        this.status.textContent = message;
        this.status.className = `status ${type}`;
        this.status.classList.remove('hidden');
        
        if (type === 'recording') {
            this.status.classList.add('pulse');
        } else {
            this.status.classList.remove('pulse');
        }
    }
    
    hideStatus() {
        this.status.classList.add('hidden');
        this.status.classList.remove('pulse');
    }
    
    async startRecording() {
        try {
            this.log('Requesting microphone access...');
            this.showStatus('Requesting microphone access...', 'processing');
            
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 44100, // Let MediaRecorder handle this
                    channelCount: 1
                }
            });
            
            this.log('Microphone access granted');
            this.log('Creating MediaRecorder...');
            
            // Try different MIME types for better compatibility
            const mimeTypes = [
                'audio/webm;codecs=opus',
                'audio/webm',
                'audio/mp4',
                'audio/ogg;codecs=opus'
            ];
            
            let selectedMimeType = '';
            for (const mimeType of mimeTypes) {
                if (MediaRecorder.isTypeSupported(mimeType)) {
                    selectedMimeType = mimeType;
                    break;
                }
            }
            
            if (!selectedMimeType) {
                throw new Error('No supported audio MIME type found');
            }
            
            this.log(`Using MIME type: ${selectedMimeType}`);
            
            this.mediaRecorder = new MediaRecorder(stream, {
                mimeType: selectedMimeType
            });
            
            this.audioChunks = [];
            this.startTime = Date.now();
            
            // Set up event handlers
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                    this.log(`Audio chunk received: ${event.data.size} bytes`);
                }
            };
            
            this.mediaRecorder.onstop = async () => {
                this.log('MediaRecorder stopped, processing audio...');
                await this.processRecording();
            };
            
            this.mediaRecorder.onerror = (event) => {
                this.log(`MediaRecorder error: ${event.error}`);
                this.showStatus(`Recording error: ${event.error}`, 'error');
            };
            
            // Start recording
            this.mediaRecorder.start(1000); // Collect data every second
            this.isRecording = true;
            
            this.startBtn.disabled = true;
            this.stopBtn.disabled = false;
            
            this.showStatus(`Recording... (Max ${this.maxDuration}s)`, 'recording');
            this.log('Recording started successfully');
            
            // Auto-stop after max duration
            setTimeout(() => {
                if (this.isRecording) {
                    this.log(`Auto-stopping recording after ${this.maxDuration} seconds`);
                    this.stopRecording();
                }
            }, this.maxDuration * 1000);
            
        } catch (error) {
            this.log(`Failed to start recording: ${error.message}`);
            this.showStatus(`Failed to start recording: ${error.message}`, 'error');
            this.resetButtons();
        }
    }
    
    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.log('Stopping recording...');
            this.showStatus('Stopping recording...', 'processing');
            this.mediaRecorder.stop();
            this.isRecording = false;
            
            // Stop all tracks
            if (this.mediaRecorder.stream) {
                this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            }
        }
    }
    
    async processRecording() {
        try {
            this.showStatus('Processing audio...', 'processing');
            
            if (this.audioChunks.length === 0) {
                throw new Error('No audio data recorded');
            }
            
            const duration = (Date.now() - this.startTime) / 1000;
            this.log(`Recording duration: ${duration.toFixed(2)} seconds`);
            
            // Create blob from chunks
            const originalBlob = new Blob(this.audioChunks, { 
                type: this.mediaRecorder.mimeType 
            });
            
            this.log(`Original audio blob: ${originalBlob.size} bytes, type: ${originalBlob.type}`);
            
            // Update audio info
            this.updateAudioInfo({
                originalFormat: originalBlob.type,
                originalSize: this.formatBytes(originalBlob.size),
                duration: `${duration.toFixed(2)}s`
            });
            
            // Convert to 16kHz WAV using your provided function
            this.log('Converting to 16kHz WAV...');
            const wavBlob = await this.convertMediaRecorderBlobToWav(originalBlob, 16000);
            
            this.log(`Converted WAV blob: ${wavBlob.size} bytes`);
            
            // Update audio info with converted data
            this.updateAudioInfo({
                convertedFormat: 'audio/wav (16kHz mono)',
                convertedSize: this.formatBytes(wavBlob.size)
            });
            
            this.showStatus('Audio converted successfully!', 'success');
            
            // Send to ASR
            await this.sendToASR(wavBlob);
            
        } catch (error) {
            this.log(`Error processing recording: ${error.message}`);
            this.showStatus(`Processing failed: ${error.message}`, 'error');
        } finally {
            this.resetButtons();
        }
    }
    
    // Your provided conversion function
    async convertMediaRecorderBlobToWav(blob, targetSampleRate = 16000) {
        this.log(`Starting conversion to ${targetSampleRate}Hz WAV...`);
        
        const arrayBuffer = await blob.arrayBuffer();
        this.log(`Audio buffer size: ${arrayBuffer.byteLength} bytes`);
        
        const audioCtx = new AudioContext();
        const decodedBuffer = await audioCtx.decodeAudioData(arrayBuffer);
        
        this.log(`Decoded audio: ${decodedBuffer.sampleRate}Hz, ${decodedBuffer.numberOfChannels} channels, ${decodedBuffer.duration.toFixed(2)}s`);
        
        // Resample
        const offlineCtx = new OfflineAudioContext(
            1, // mono
            decodedBuffer.duration * targetSampleRate,
            targetSampleRate
        );
        
        const source = offlineCtx.createBufferSource();
        source.buffer = decodedBuffer;
        source.connect(offlineCtx.destination);
        source.start(0);
        
        this.log('Resampling audio...');
        const resampledBuffer = await offlineCtx.startRendering();
        
        this.log(`Resampled to: ${resampledBuffer.sampleRate}Hz, ${resampledBuffer.numberOfChannels} channels`);
        
        // Encode to WAV
        this.log('Encoding to WAV format...');
        return this.encodeWav(resampledBuffer, targetSampleRate);
    }
    
    // Your provided WAV encoding function
    encodeWav(audioBuffer, sampleRate) {
        const channelData = audioBuffer.getChannelData(0); // mono
        const length = channelData.length * 2;
        const buffer = new ArrayBuffer(44 + length);
        const view = new DataView(buffer);
        
        const writeString = (offset, str) => {
            for (let i = 0; i < str.length; i++) {
                view.setUint8(offset + i, str.charCodeAt(i));
            }
        };
        
        // RIFF header
        writeString(0, 'RIFF');
        view.setUint32(4, 36 + length, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true); // Subchunk1Size
        view.setUint16(20, 1, true);  // AudioFormat (PCM)
        view.setUint16(22, 1, true);  // NumChannels
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, sampleRate * 2, true); // ByteRate
        view.setUint16(32, 2, true);  // BlockAlign
        view.setUint16(34, 16, true); // BitsPerSample
        writeString(36, 'data');
        view.setUint32(40, length, true);
        
        // PCM samples
        let offset = 44;
        for (let i = 0; i < channelData.length; i++) {
            const sample = Math.max(-1, Math.min(1, channelData[i]));
            view.setInt16(offset, sample * 0x7FFF, true);
            offset += 2;
        }
        
        this.log(`WAV encoding complete: ${buffer.byteLength} bytes`);
        return new Blob([view], { type: 'audio/wav' });
    }
    
    async sendToASR(audioBlob) {
        try {
            const apiKey = this.apiKeyInput.value.trim();
            if (!apiKey) {
                this.log('No API key provided, skipping ASR request');
                this.asrResult.innerHTML = '<em style="color: #e53e3e;">Please provide an API key to test ASR</em>';
                return;
            }
            
            this.log('Sending audio to ASR service...');
            this.showStatus('Sending to ASR...', 'processing');
            
            const language = this.languageSelect.value;
            
            // Convert blob to base64
            const arrayBuffer = await audioBlob.arrayBuffer();
            const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
            
            const payload = {
                audio: [{
                    audioContent: base64Audio
                }],
                config: {
                    language: {
                        sourceLanguage: language
                    },
                    serviceId: "ai4bharat/indictasr",
                    audioFormat: "wav",
                    encoding: "LINEAR16",
                    samplingRate: 16000
                }
            };
            
            this.log(`Sending ${audioBlob.size} bytes to ASR (language: ${language})`);
            
            const response = await fetch('https://13.203.149.17/services/inference/asr?serviceId=ai4bharat/indictasr', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify(payload)
            });
            
            if (!response.ok) {
                throw new Error(`ASR request failed: ${response.status} ${response.statusText}`);
            }
            
            const result = await response.json();
            this.log('ASR response received');
            
            if (result.output && result.output.length > 0 && result.output[0].source) {
                const transcript = result.output[0].source;
                this.log(`ASR transcript: "${transcript}"`);
                this.asrResult.innerHTML = `<strong>Transcript:</strong> ${transcript}`;
                this.showStatus('ASR completed successfully!', 'success');
            } else {
                this.log('No transcript in ASR response');
                this.asrResult.innerHTML = '<em style="color: #e53e3e;">No transcript received from ASR</em>';
                this.showStatus('ASR completed but no transcript received', 'error');
            }
            
        } catch (error) {
            this.log(`ASR request failed: ${error.message}`);
            this.asrResult.innerHTML = `<em style="color: #e53e3e;">ASR Error: ${error.message}</em>`;
            this.showStatus(`ASR failed: ${error.message}`, 'error');
        }
    }
    
    testFileUpload() {
        this.log('Opening file picker for testing...');
        this.fileInput.click();
    }
    
    async handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        try {
            this.log(`Testing file upload: ${file.name} (${this.formatBytes(file.size)})`);
            this.showStatus('Processing uploaded file...', 'processing');
            
            // Update audio info
            this.updateAudioInfo({
                originalFormat: file.type,
                originalSize: this.formatBytes(file.size),
                duration: 'Unknown'
            });
            
            // Convert file to blob and process
            const blob = new Blob([file], { type: file.type });
            const wavBlob = await this.convertMediaRecorderBlobToWav(blob, 16000);
            
            this.updateAudioInfo({
                convertedFormat: 'audio/wav (16kHz mono)',
                convertedSize: this.formatBytes(wavBlob.size)
            });
            
            this.showStatus('File converted successfully!', 'success');
            
            // Send to ASR
            await this.sendToASR(wavBlob);
            
        } catch (error) {
            this.log(`File processing failed: ${error.message}`);
            this.showStatus(`File processing failed: ${error.message}`, 'error');
        }
    }
    
    updateAudioInfo(info) {
        Object.keys(info).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = info[key];
            }
        });
        this.audioInfo.classList.remove('hidden');
    }
    
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    resetButtons() {
        this.startBtn.disabled = false;
        this.stopBtn.disabled = true;
    }
}

// Utility functions
function clearLog() {
    document.getElementById('log').textContent = 'Log cleared...\n';
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.micTest = new MicrophoneRecordingTest();
});

// Add some helpful global functions for debugging
window.testMicrophone = () => window.micTest.startRecording();
window.testFile = () => window.micTest.testFileUpload();
