# 🎤 Microphone Recording Test App

A dedicated standalone application for testing microphone recording reliability with the Dhruva Platform ASR service.

## 🚀 Quick Start

1. **Start the server:**
   ```bash
   npm start
   ```

2. **Open in browser:**
   ```
   http://localhost:8080
   ```

3. **Configure API Key:**
   - Use this API key: `resEY0jeJ5KqTFyV3xaLineuRMgBqvsSneEiUG_Ic4yJVVrPFSPGMlNM3ySVzznE`
   - Paste it in the "API Key" field in the app

## 🎯 Features

### Core Functionality
- **Microphone Recording**: Record audio directly from your microphone
- **16kHz WAV Conversion**: Uses your provided `convertMediaRecorderBlobToWav` function
- **ASR Integration**: Sends converted audio to Dhruva Platform ASR service
- **File Upload Testing**: Compare microphone recording with file upload
- **Real-time Feedback**: Detailed logging and status updates

### Audio Processing Pipeline
```
MediaRecorder → WebM/MP4 Blob → AudioContext → 16kHz Resampling → WAV Encoding → ASR Service
```

### Your Provided Code Integration
- ✅ `convertMediaRecorderBlobToWav(blob, targetSampleRate = 16000)`
- ✅ `encodeWav(audioBuffer, sampleRate)` 
- ✅ Exact same flow as working file upload

## 🔧 Configuration Options

- **Language Selection**: Choose from 10 Indian languages
- **Recording Duration**: Set max recording time (5-120 seconds)
- **API Key**: Use extracted MongoDB API key for ASR requests

## 📊 Testing Capabilities

### Microphone Recording Test
1. Click "🎤 Start Recording"
2. Speak for a few seconds
3. Click "⏹️ Stop Recording"
4. Watch the conversion process in real-time
5. See ASR transcription results

### File Upload Comparison
1. Click "🧪 Test File Upload"
2. Select an audio file
3. Compare results with microphone recording

### Debug Information
- **Audio Format Details**: Original vs converted format info
- **File Sizes**: Before and after conversion
- **Processing Logs**: Step-by-step conversion process
- **Error Handling**: Detailed error messages with recovery steps

## 🎵 Audio Processing Details

### Input Formats Supported
- WebM (with Opus codec)
- MP4
- OGG
- Any format supported by MediaRecorder

### Output Format
- **Format**: WAV (PCM 16-bit)
- **Sample Rate**: 16kHz
- **Channels**: Mono (1 channel)
- **Encoding**: Linear PCM

### Conversion Process
1. **Decode**: AudioContext.decodeAudioData()
2. **Resample**: OfflineAudioContext at 16kHz
3. **Encode**: Custom WAV encoder with proper headers
4. **Validate**: Size and format verification

## 🐛 Debugging Features

### Real-time Logging
- Microphone access requests
- MediaRecorder events
- Audio conversion steps
- ASR API requests and responses
- Error details with recovery suggestions

### Audio Information Display
- Original audio format and size
- Converted audio format and size
- Recording duration
- Sample rate confirmation

### Error Handling
- Permission denied scenarios
- Microphone not found
- Audio conversion failures
- Network connectivity issues
- ASR service errors

## 🔍 Troubleshooting

### Common Issues

1. **Microphone Permission Denied**
   - Check browser permissions
   - Allow microphone access
   - Refresh the page

2. **No Audio Recorded**
   - Ensure microphone is connected
   - Check system audio settings
   - Try a different browser

3. **Conversion Failures**
   - Check browser console for errors
   - Verify AudioContext support
   - Try shorter recordings

4. **ASR Errors**
   - Verify API key is correct
   - Check network connectivity
   - Ensure audio format is supported

## 🎯 Expected Results

### Successful Flow
1. ✅ Microphone access granted
2. ✅ Audio recorded successfully
3. ✅ Conversion to 16kHz WAV completed
4. ✅ ASR transcription received
5. ✅ Results displayed in UI

### Performance Metrics
- **Recording Quality**: Clear audio capture
- **Conversion Speed**: < 2 seconds for 30s audio
- **ASR Accuracy**: Comparable to file upload
- **Error Rate**: < 5% for supported browsers

## 🌐 Browser Compatibility

### Supported Browsers
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+

### Required Features
- MediaRecorder API
- AudioContext API
- OfflineAudioContext API
- getUserMedia API

## 📝 API Integration

### ASR Endpoint
```
POST https://*************/services/inference/asr?serviceId=ai4bharat/indictasr
```

### Request Format
```json
{
  "audio": [{"audioContent": "base64_encoded_wav"}],
  "config": {
    "language": {"sourceLanguage": "en"},
    "serviceId": "ai4bharat/indictasr",
    "audioFormat": "wav",
    "encoding": "LINEAR16",
    "samplingRate": 16000
  }
}
```

## 🎉 Success Criteria

The test app helps verify that:
- ✅ Microphone recording works reliably
- ✅ Audio conversion produces correct 16kHz WAV format
- ✅ ASR service accepts and processes the audio
- ✅ Transcription quality matches file upload results
- ✅ Error handling provides clear feedback

This isolated test environment eliminates complexity and focuses specifically on the microphone recording and conversion pipeline.
