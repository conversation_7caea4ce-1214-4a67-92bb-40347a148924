<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
</head>
<body>
    <h1>Debug Test</h1>
    <button onclick="testConversion()">Test Conversion</button>
    <div id="result"></div>

    <script>
        function log(message) {
            console.log(message);
            document.getElementById('result').innerHTML += message + '<br>';
        }

        async function convertMediaRecorderBlobToWav(blob, targetSampleRate = 16000) {
            try {
                log('Starting conversion...');
                
                const arrayBuffer = await blob.arrayBuffer();
                log(`Buffer size: ${arrayBuffer.byteLength}`);
                
                const audioCtx = new AudioContext();
                const decodedBuffer = await audioCtx.decodeAudioData(arrayBuffer);
                log(`Decoded: ${decodedBuffer.sampleRate}Hz`);
                
                const offlineCtx = new OfflineAudioContext(1, decodedBuffer.duration * targetSampleRate, targetSampleRate);
                const source = offlineCtx.createBufferSource();
                source.buffer = decodedBuffer;
                source.connect(offlineCtx.destination);
                source.start(0);
                
                const resampledBuffer = await offlineCtx.startRendering();
                log(`Resampled: ${resampledBuffer.sampleRate}Hz`);
                
                return encodeWav(resampledBuffer, targetSampleRate);
            } catch (error) {
                log(`Error: ${error.message}`);
                throw error;
            }
        }

        function encodeWav(audioBuffer, sampleRate) {
            try {
                log('Encoding WAV...');
                
                const channelData = audioBuffer.getChannelData(0);
                const length = channelData.length * 2;
                const buffer = new ArrayBuffer(44 + length);
                const view = new DataView(buffer);

                const writeString = (offset, str) => {
                    for (let i = 0; i < str.length; i++) {
                        view.setUint8(offset + i, str.charCodeAt(i));
                    }
                };

                writeString(0, 'RIFF');
                view.setUint32(4, 36 + length, true);
                writeString(8, 'WAVE');
                writeString(12, 'fmt ');
                view.setUint32(16, 16, true);
                view.setUint16(20, 1, true);
                view.setUint16(22, 1, true);
                view.setUint32(24, sampleRate, true);
                view.setUint32(28, sampleRate * 2, true);
                view.setUint16(32, 2, true);
                view.setUint16(34, 16, true);
                writeString(36, 'data');
                view.setUint32(40, length, true);

                let offset = 44;
                for (let i = 0; i < channelData.length; i++) {
                    const sample = Math.max(-1, Math.min(1, channelData[i]));
                    view.setInt16(offset, sample * 0x7FFF, true);
                    offset += 2;
                }

                log(`WAV complete: ${buffer.byteLength} bytes`);
                return new Blob([view], { type: 'audio/wav' });
            } catch (error) {
                log(`WAV error: ${error.message}`);
                throw error;
            }
        }

        function testConversion() {
            try {
                log('=== Starting Test ===');
                
                // Create test audio
                const audioCtx = new AudioContext();
                const buffer = audioCtx.createBuffer(1, 44100, 44100); // 1 second
                const channelData = buffer.getChannelData(0);
                
                for (let i = 0; i < channelData.length; i++) {
                    channelData[i] = Math.sin(2 * Math.PI * 440 * i / 44100) * 0.5;
                }
                
                log('Test buffer created');
                
                const wavBlob = encodeWav(buffer, 44100);
                log(`Success! WAV blob: ${wavBlob.size} bytes`);
                
            } catch (error) {
                log(`Test failed: ${error.message}`);
                console.error(error);
            }
        }

        log('Debug test ready');
    </script>
</body>
</html>
